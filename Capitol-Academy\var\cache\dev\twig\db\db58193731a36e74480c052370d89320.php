<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/instructor/show.html.twig */
class __TwigTemplate_6d345eec8644f30d0900f074e34ca826 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
            'stylesheets' => [$this, 'block_stylesheets'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/show.html.twig"));

        // line 1
        yield from $this->load("admin/instructor/show.html.twig", 1, "2018458980")->unwrap()->yield(CoreExtension::merge($context, ["entity_name" => "Instructor", "entity_title" => CoreExtension::getAttribute($this->env, $this->source,         // line 3
(isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 3, $this->source); })()), "name", [], "any", false, false, false, 3), "entity_code" => CoreExtension::getAttribute($this->env, $this->source,         // line 4
(isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 4, $this->source); })()), "emailPrefix", [], "any", false, false, false, 4), "entity_icon" => "fas fa-chalkboard-teacher", "breadcrumb_items" => [["path" => "admin_dashboard", "title" => "Home"], ["path" => "admin_instructor_index", "title" => "Instructors"], ["title" => CoreExtension::getAttribute($this->env, $this->source,         // line 9
(isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 9, $this->source); })()), "name", [], "any", false, false, false, 9), "active" => true]], "back_path" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_index"), "print_function" => "printInstructorDetails"]));
        // line 185
        yield "
";
        // line 186
        yield from $this->unwrap()->yieldBlock('stylesheets', $context, $blocks);
        // line 199
        yield "
";
        // line 200
        yield from $this->unwrap()->yieldBlock('javascripts', $context, $blocks);
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    // line 186
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 187
        yield "<style>
/* Remove bold font-weight from enhanced-display-field elements */
.enhanced-display-field {
    font-weight: normal !important;
}

/* Remove bold font-weight from form labels */
.form-label {
    font-weight: normal !important;
}
</style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 200
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 201
        yield "<script>
// Print function for the preview layout
function printInstructorDetails() {
    window.print();
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/instructor/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  122 => 201,  109 => 200,  87 => 187,  74 => 186,  63 => 200,  60 => 199,  58 => 186,  55 => 185,  53 => 9,  52 => 4,  51 => 3,  50 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Instructor',
    'entity_title': instructor.name,
    'entity_code': instructor.emailPrefix,
    'entity_icon': 'fas fa-chalkboard-teacher',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_instructor_index', 'title': 'Instructors'},
        {'title': instructor.name, 'active': true}
    ],
    'back_path': path('admin_instructor_index'),
    'print_function': 'printInstructorDetails'
} %}

{% block preview_content %}

    <!-- Instructor Profile Information -->
    <div class=\"row\">
        <!-- Name and Email -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-user text-primary mr-1\"></i>
                    Full Name
                </label>
                <div class=\"enhanced-display-field\">
                    {{ instructor.name }}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-envelope text-primary mr-1\"></i>
                    Email Address
                </label>
                <div class=\"enhanced-display-field\">
                    <a href=\"mailto:{{ instructor.email }}\" class=\"text-decoration-none\" style=\"color: #011a2d;\">{{ instructor.email }}</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Specialization (Full Width) -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-star text-primary mr-1\"></i>
            Specialization
        </label>
        <div class=\"enhanced-display-field\">
            {{ instructor.specialization ?? 'Not specified' }}
        </div>
    </div>

    <!-- Phone and Display Order Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-phone text-primary mr-1\"></i>
                    Phone
                </label>
                <div class=\"enhanced-display-field\">
                    {{ instructor.phone ?? 'Not provided' }}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                    Display Order
                </label>
                <div class=\"enhanced-display-field\">
                    {{ instructor.displayOrder ?? 'Not set' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Biography -->
    {% if instructor.bio %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-file-text text-primary mr-1\"></i>
            Biography
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 120px;\">
            {{ instructor.bio|nl2br }}
        </div>
    </div>
    {% endif %}

    <!-- Qualifications -->
    {% if instructor.qualifications|length > 0 %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
            Qualifications
        </label>
        <div class=\"enhanced-display-field\">
            <ul class=\"list-unstyled mb-0\">
                {% for qualification in instructor.qualifications %}
                    <li class=\"mb-2\">
                        <i class=\"fas fa-certificate text-primary me-2\"></i>{{ qualification }}
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    {% endif %}

    <!-- Achievements -->
    {% if instructor.achievements|length > 0 %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-trophy text-primary mr-1\"></i>
            Achievements
        </label>
        <div class=\"enhanced-display-field\">
            <ul class=\"list-unstyled mb-0\">
                {% for achievement in instructor.achievements %}
                    <li class=\"mb-2\">
                        <i class=\"fas fa-award text-warning me-2\"></i>{{ achievement }}
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    {% endif %}

    <!-- LinkedIn URL -->
    {% if instructor.linkedinUrl %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fab fa-linkedin text-primary mr-1\"></i>
            LinkedIn Profile
        </label>
        <div class=\"enhanced-display-field\">
            <a href=\"{{ instructor.linkedinUrl }}\" target=\"_blank\" class=\"text-decoration-none\" style=\"color: #011a2d;\">
                <i class=\"fas fa-external-link-alt me-2\"></i>View LinkedIn Profile
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Status and Created Date Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                    Status
                </label>
                <div class=\"enhanced-display-field\">
                    {% if instructor.isActive %}
                        <span class=\"badge bg-success\">Active</span>
                    {% else %}
                        <span class=\"badge bg-secondary\">Inactive</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Created Date
                </label>
                <div class=\"enhanced-display-field\">
                    {% if instructor.createdAt %}
                        {{ instructor.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                    {% else %}
                        Not available
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% endembed %}

{% block stylesheets %}
<style>
/* Remove bold font-weight from enhanced-display-field elements */
.enhanced-display-field {
    font-weight: normal !important;
}

/* Remove bold font-weight from form labels */
.form-label {
    font-weight: normal !important;
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Print function for the preview layout
function printInstructorDetails() {
    window.print();
}
</script>
{% endblock %}
", "admin/instructor/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\instructor\\show.html.twig");
    }
}


/* admin/instructor/show.html.twig */
class __TwigTemplate_6d345eec8644f30d0900f074e34ca826___2018458980 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'preview_content' => [$this, 'block_preview_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "components/admin_preview_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/show.html.twig"));

        $this->parent = $this->load("components/admin_preview_layout.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 15
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_preview_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "preview_content"));

        // line 16
        yield "
    <!-- Instructor Profile Information -->
    <div class=\"row\">
        <!-- Name and Email -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-user text-primary mr-1\"></i>
                    Full Name
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 27
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 27, $this->source); })()), "name", [], "any", false, false, false, 27), "html", null, true);
        yield "
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-envelope text-primary mr-1\"></i>
                    Email Address
                </label>
                <div class=\"enhanced-display-field\">
                    <a href=\"mailto:";
        // line 39
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 39, $this->source); })()), "email", [], "any", false, false, false, 39), "html", null, true);
        yield "\" class=\"text-decoration-none\" style=\"color: #011a2d;\">";
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 39, $this->source); })()), "email", [], "any", false, false, false, 39), "html", null, true);
        yield "</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Specialization (Full Width) -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-star text-primary mr-1\"></i>
            Specialization
        </label>
        <div class=\"enhanced-display-field\">
            ";
        // line 52
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["instructor"] ?? null), "specialization", [], "any", true, true, false, 52) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 52, $this->source); })()), "specialization", [], "any", false, false, false, 52)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 52, $this->source); })()), "specialization", [], "any", false, false, false, 52), "html", null, true)) : ("Not specified"));
        yield "
        </div>
    </div>

    <!-- Phone and Display Order Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-phone text-primary mr-1\"></i>
                    Phone
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 65
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["instructor"] ?? null), "phone", [], "any", true, true, false, 65) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 65, $this->source); })()), "phone", [], "any", false, false, false, 65)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 65, $this->source); })()), "phone", [], "any", false, false, false, 65), "html", null, true)) : ("Not provided"));
        yield "
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                    Display Order
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 77
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["instructor"] ?? null), "displayOrder", [], "any", true, true, false, 77) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 77, $this->source); })()), "displayOrder", [], "any", false, false, false, 77)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 77, $this->source); })()), "displayOrder", [], "any", false, false, false, 77), "html", null, true)) : ("Not set"));
        yield "
                </div>
            </div>
        </div>
    </div>

    <!-- Biography -->
    ";
        // line 84
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 84, $this->source); })()), "bio", [], "any", false, false, false, 84)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 85
            yield "    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-file-text text-primary mr-1\"></i>
            Biography
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 120px;\">
            ";
            // line 91
            yield Twig\Extension\CoreExtension::nl2br($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 91, $this->source); })()), "bio", [], "any", false, false, false, 91), "html", null, true));
            yield "
        </div>
    </div>
    ";
        }
        // line 95
        yield "
    <!-- Qualifications -->
    ";
        // line 97
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 97, $this->source); })()), "qualifications", [], "any", false, false, false, 97)) > 0)) {
            // line 98
            yield "    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
            Qualifications
        </label>
        <div class=\"enhanced-display-field\">
            <ul class=\"list-unstyled mb-0\">
                ";
            // line 105
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 105, $this->source); })()), "qualifications", [], "any", false, false, false, 105));
            foreach ($context['_seq'] as $context["_key"] => $context["qualification"]) {
                // line 106
                yield "                    <li class=\"mb-2\">
                        <i class=\"fas fa-certificate text-primary me-2\"></i>";
                // line 107
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["qualification"], "html", null, true);
                yield "
                    </li>
                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['qualification'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 110
            yield "            </ul>
        </div>
    </div>
    ";
        }
        // line 114
        yield "
    <!-- Achievements -->
    ";
        // line 116
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 116, $this->source); })()), "achievements", [], "any", false, false, false, 116)) > 0)) {
            // line 117
            yield "    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-trophy text-primary mr-1\"></i>
            Achievements
        </label>
        <div class=\"enhanced-display-field\">
            <ul class=\"list-unstyled mb-0\">
                ";
            // line 124
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 124, $this->source); })()), "achievements", [], "any", false, false, false, 124));
            foreach ($context['_seq'] as $context["_key"] => $context["achievement"]) {
                // line 125
                yield "                    <li class=\"mb-2\">
                        <i class=\"fas fa-award text-warning me-2\"></i>";
                // line 126
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["achievement"], "html", null, true);
                yield "
                    </li>
                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['achievement'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 129
            yield "            </ul>
        </div>
    </div>
    ";
        }
        // line 133
        yield "
    <!-- LinkedIn URL -->
    ";
        // line 135
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 135, $this->source); })()), "linkedinUrl", [], "any", false, false, false, 135)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 136
            yield "    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fab fa-linkedin text-primary mr-1\"></i>
            LinkedIn Profile
        </label>
        <div class=\"enhanced-display-field\">
            <a href=\"";
            // line 142
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 142, $this->source); })()), "linkedinUrl", [], "any", false, false, false, 142), "html", null, true);
            yield "\" target=\"_blank\" class=\"text-decoration-none\" style=\"color: #011a2d;\">
                <i class=\"fas fa-external-link-alt me-2\"></i>View LinkedIn Profile
            </a>
        </div>
    </div>
    ";
        }
        // line 148
        yield "
    <!-- Status and Created Date Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                    Status
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 158
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 158, $this->source); })()), "isActive", [], "any", false, false, false, 158)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 159
            yield "                        <span class=\"badge bg-success\">Active</span>
                    ";
        } else {
            // line 161
            yield "                        <span class=\"badge bg-secondary\">Inactive</span>
                    ";
        }
        // line 163
        yield "                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Created Date
                </label>
                <div class=\"enhanced-display-field\">
                    ";
        // line 174
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 174, $this->source); })()), "createdAt", [], "any", false, false, false, 174)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 175
            yield "                        ";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 175, $this->source); })()), "createdAt", [], "any", false, false, false, 175), "F j, Y \\a\\t g:i A"), "html", null, true);
            yield "
                    ";
        } else {
            // line 177
            yield "                        Not available
                    ";
        }
        // line 179
        yield "                </div>
            </div>
        </div>
    </div>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/instructor/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  683 => 179,  679 => 177,  673 => 175,  671 => 174,  658 => 163,  654 => 161,  650 => 159,  648 => 158,  636 => 148,  627 => 142,  619 => 136,  617 => 135,  613 => 133,  607 => 129,  598 => 126,  595 => 125,  591 => 124,  582 => 117,  580 => 116,  576 => 114,  570 => 110,  561 => 107,  558 => 106,  554 => 105,  545 => 98,  543 => 97,  539 => 95,  532 => 91,  524 => 85,  522 => 84,  512 => 77,  497 => 65,  481 => 52,  463 => 39,  448 => 27,  435 => 16,  422 => 15,  399 => 1,  122 => 201,  109 => 200,  87 => 187,  74 => 186,  63 => 200,  60 => 199,  58 => 186,  55 => 185,  53 => 9,  52 => 4,  51 => 3,  50 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Instructor',
    'entity_title': instructor.name,
    'entity_code': instructor.emailPrefix,
    'entity_icon': 'fas fa-chalkboard-teacher',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_instructor_index', 'title': 'Instructors'},
        {'title': instructor.name, 'active': true}
    ],
    'back_path': path('admin_instructor_index'),
    'print_function': 'printInstructorDetails'
} %}

{% block preview_content %}

    <!-- Instructor Profile Information -->
    <div class=\"row\">
        <!-- Name and Email -->
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-user text-primary mr-1\"></i>
                    Full Name
                </label>
                <div class=\"enhanced-display-field\">
                    {{ instructor.name }}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-envelope text-primary mr-1\"></i>
                    Email Address
                </label>
                <div class=\"enhanced-display-field\">
                    <a href=\"mailto:{{ instructor.email }}\" class=\"text-decoration-none\" style=\"color: #011a2d;\">{{ instructor.email }}</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Specialization (Full Width) -->
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-star text-primary mr-1\"></i>
            Specialization
        </label>
        <div class=\"enhanced-display-field\">
            {{ instructor.specialization ?? 'Not specified' }}
        </div>
    </div>

    <!-- Phone and Display Order Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-phone text-primary mr-1\"></i>
                    Phone
                </label>
                <div class=\"enhanced-display-field\">
                    {{ instructor.phone ?? 'Not provided' }}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-sort-numeric-up text-primary mr-1\"></i>
                    Display Order
                </label>
                <div class=\"enhanced-display-field\">
                    {{ instructor.displayOrder ?? 'Not set' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Biography -->
    {% if instructor.bio %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-file-text text-primary mr-1\"></i>
            Biography
        </label>
        <div class=\"enhanced-display-field\" style=\"line-height: 1.6; min-height: 120px;\">
            {{ instructor.bio|nl2br }}
        </div>
    </div>
    {% endif %}

    <!-- Qualifications -->
    {% if instructor.qualifications|length > 0 %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-graduation-cap text-primary mr-1\"></i>
            Qualifications
        </label>
        <div class=\"enhanced-display-field\">
            <ul class=\"list-unstyled mb-0\">
                {% for qualification in instructor.qualifications %}
                    <li class=\"mb-2\">
                        <i class=\"fas fa-certificate text-primary me-2\"></i>{{ qualification }}
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    {% endif %}

    <!-- Achievements -->
    {% if instructor.achievements|length > 0 %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fas fa-trophy text-primary mr-1\"></i>
            Achievements
        </label>
        <div class=\"enhanced-display-field\">
            <ul class=\"list-unstyled mb-0\">
                {% for achievement in instructor.achievements %}
                    <li class=\"mb-2\">
                        <i class=\"fas fa-award text-warning me-2\"></i>{{ achievement }}
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    {% endif %}

    <!-- LinkedIn URL -->
    {% if instructor.linkedinUrl %}
    <div class=\"form-group\">
        <label class=\"form-label\">
            <i class=\"fab fa-linkedin text-primary mr-1\"></i>
            LinkedIn Profile
        </label>
        <div class=\"enhanced-display-field\">
            <a href=\"{{ instructor.linkedinUrl }}\" target=\"_blank\" class=\"text-decoration-none\" style=\"color: #011a2d;\">
                <i class=\"fas fa-external-link-alt me-2\"></i>View LinkedIn Profile
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Status and Created Date Row -->
    <div class=\"row\">
        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                    Status
                </label>
                <div class=\"enhanced-display-field\">
                    {% if instructor.isActive %}
                        <span class=\"badge bg-success\">Active</span>
                    {% else %}
                        <span class=\"badge bg-secondary\">Inactive</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class=\"col-md-6\">
            <div class=\"form-group\">
                <label class=\"form-label\">
                    <i class=\"fas fa-calendar text-primary mr-1\"></i>
                    Created Date
                </label>
                <div class=\"enhanced-display-field\">
                    {% if instructor.createdAt %}
                        {{ instructor.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                    {% else %}
                        Not available
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% endembed %}

{% block stylesheets %}
<style>
/* Remove bold font-weight from enhanced-display-field elements */
.enhanced-display-field {
    font-weight: normal !important;
}

/* Remove bold font-weight from form labels */
.form-label {
    font-weight: normal !important;
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Print function for the preview layout
function printInstructorDetails() {
    window.print();
}
</script>
{% endblock %}
", "admin/instructor/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\instructor\\show.html.twig");
    }
}
