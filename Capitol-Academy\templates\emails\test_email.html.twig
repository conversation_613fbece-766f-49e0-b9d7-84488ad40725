<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capitol Academy - Email Test</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #011a2d;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            color: #011a2d;
            font-size: 28px;
            font-weight: bold;
            font-family: 'Montserrat', sans-serif;
        }
        .content {
            margin-bottom: 30px;
        }
        .test-info {
            background: #f6f7f9;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #a90418;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .success-badge {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">Capitol Academy</div>
            <p style="margin: 10px 0 0 0; color: #6c757d;">Professional Trading Education</p>
        </div>

        <div class="content">
            <h2 style="color: #011a2d; margin-bottom: 20px;">Email System Test</h2>
            
            <div class="success-badge">✓ Email System Working</div>
            
            <p>This is a test email to verify that the Capitol Academy email system is functioning correctly.</p>
            
            <div class="test-info">
                <h4 style="color: #a90418; margin-top: 0;">Test Details:</h4>
                <ul style="margin: 10px 0;">
                    <li><strong>Test Time:</strong> {{ test_time|date('F j, Y \\a\\t g:i A') }}</li>
                    <li><strong>Initiated by:</strong> {{ admin_name }}</li>
                    <li><strong>From Email:</strong> <EMAIL></li>
                    <li><strong>To Email:</strong> <EMAIL></li>
                    <li><strong>SMTP Configuration:</strong> Gmail SMTP with App Password</li>
                </ul>
            </div>

            <p>If you received this email, it means:</p>
            <ul>
                <li>✓ SMTP configuration is correct</li>
                <li>✓ Email credentials are working</li>
                <li>✓ Symfony Mailer is properly configured</li>
                <li>✓ Email delivery is successful</li>
            </ul>

            <p style="margin-top: 30px;">
                <strong>Next Steps:</strong><br>
                You can now proceed with confidence that the email system is working correctly for:
            </p>
            <ul>
                <li>Contact form notifications</li>
                <li>User registration confirmations</li>
                <li>Password reset emails</li>
                <li>Course enrollment notifications</li>
            </ul>
        </div>

        <div class="footer">
            <p>
                <strong>Capitol Academy</strong><br>
                Professional Trading Education Platform<br>
                Registration #1264639G
            </p>
            <p style="margin-top: 15px; font-size: 12px;">
                This is an automated test email from the Capitol Academy admin panel.
            </p>
        </div>
    </div>
</body>
</html>
